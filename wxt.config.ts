import { defineConfig } from 'wxt';

// See https://wxt.dev/api/config.html
export default defineConfig({
  extensionApi: 'chrome',
  modules: ['@wxt-dev/module-vue'],
  manifest: {
    name: 'Fillify - AI Form Filler with Ollama,DeepSeek, OpenAI, Claude, Gemini, and More',
    version: '1.0.7',
    description: 'AI-Powered Forms, Emails & Bug Reports Assistant',
    permissions: [
      'storage',
      'activeTab',
      'cookies',
      'declarativeNetRequest',
      'contextMenus'
    ],
    host_permissions: [
      'https://api.openai.com/*',
      'https://api.anthropic.com/*',
      'https://api.moonshot.cn/*',
      'https://api.deepseek.com/*',
      'https://generativelanguage.googleapis.com/*',
      'https://openrouter.ai/*',
      'https://fillify.tech/*',
      'http://localhost:11434/*'  // 添加 Ollama 的特定端口访问权限
    ],
    declarative_net_request: {
      rule_resources: [{
        id: 'ollama_cors_rules',
        enabled: true,
        path: 'rules.json'
      }]
    },
    icons: {
      16: '/icon/icon16.png',
      32: '/icon/icon32.png',
      48: '/icon/icon48.png',
      128: '/icon/icon128.png'
    },
    action: {
      default_icon: {
        16: '/icon/icon16.png',
        32: '/icon/icon32.png',
        48: '/icon/icon48.png',
        128: '/icon/icon128.png'
      },
      default_title: 'Fillify'
      // 明确不设置 default_popup，让点击图标触发 onClicked 事件
    },
    options_ui: {
      page: 'settings.html',
      open_in_tab: true
    }
  },
  hooks: {
    'build:manifestGenerated': (wxt, manifest) => {
      // 移除 default_popup，让点击图标触发 onClicked 事件
      if (manifest.action && manifest.action.default_popup) {
        delete manifest.action.default_popup;
        console.log('Removed default_popup from manifest');
      }
    }
  },
  entrypointsDir: './entrypoints',
  // 手动定义入口点，避免自动添加 default_popup
  entrypoints: [
    {
      type: 'popup',
      name: 'error-popup',
      inputPath: 'entrypoints/error-popup/index.html',
      outputDir: 'error-popup'
    }
  ]
});
