<script lang="ts" setup>
/// <reference types="chrome"/>

import { ref, onMounted } from 'vue'

// 状态管理
const errorTitle = ref('Page Analysis Issue')
const errorMessage = ref('This page cannot be analyzed by Fillify.')
const showRetryButton = ref(false)
const errorReason = ref('')

// 更新错误信息的函数
const updateErrorInfo = (reason: string, pageStatus?: any) => {
  errorReason.value = reason

  // 统一使用您设计的文案
  errorTitle.value = "Oops! This page can't be analyzed right now 🙈"
  errorMessage.value = "It seems to be a blank tab or a special settings page. Try refreshing the page or opening another website to continue."

  // 根据不同情况决定是否显示重试按钮
  switch (reason) {
    case 'no_form_fields':
    case 'no_active_tab':
      showRetryButton.value = false
      break
    default:
      showRetryButton.value = true
  }
}

// 事件处理函数
const handleRetry = async () => {
  try {
    // 获取当前标签页
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.id) {
      return
    }

    // 重新检查页面状态
    const pageStatusResponse = await chrome.tabs.sendMessage(tab.id, { type: 'checkPageStatus' })
      .catch(() => {
        return {
          isValid: false,
          needsRefresh: true,
          hasFormFields: false,
          error: 'Communication failed'
        };
      });

    if (pageStatusResponse?.isValid && pageStatusResponse?.hasFormFields) {
      // 页面现在可以分析了，关闭popup
      window.close()
    } else {
      // 页面仍然有问题，更新错误信息
      updateErrorInfo('page_still_invalid', pageStatusResponse)
    }
  } catch (error) {
    console.error('Error during retry:', error)
  }
}

const handleClose = () => {
  window.close()
}

// 初始化函数
onMounted(async () => {
  try {
    // 检查是否是由于注入popup错误触发的
    const errorTrigger = await chrome.storage.local.get('popup_error_trigger');
    if (errorTrigger.popup_error_trigger) {
      const trigger = errorTrigger.popup_error_trigger;
      // 检查时间戳，确保是最近的错误（5秒内）
      if (Date.now() - trigger.timestamp < 5000) {
        console.log('Popup opened due to in-page popup error:', trigger.reason);
        updateErrorInfo(trigger.reason, trigger.pageStatus);
        
        // 清除错误触发信息
        chrome.storage.local.remove('popup_error_trigger');
        return;
      }
    }

    // 如果没有错误触发信息，检查当前页面状态
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.id) {
      updateErrorInfo('no_active_tab');
      return;
    }

    // 检查页面状态
    const pageStatusResponse = await chrome.tabs.sendMessage(tab.id, { type: 'checkPageStatus' })
      .catch(() => {
        return {
          isValid: false,
          needsRefresh: true,
          hasFormFields: false,
          error: 'Communication failed'
        };
      });

    if (!pageStatusResponse?.isValid) {
      updateErrorInfo('page_invalid', pageStatusResponse);
    } else if (pageStatusResponse.needsRefresh) {
      updateErrorInfo('page_needs_refresh', pageStatusResponse);
    } else if (!pageStatusResponse.hasFormFields) {
      updateErrorInfo('no_form_fields', pageStatusResponse);
    } else {
      // 页面状态正常，这是用户正常点击图标，应该打开注入popup
      console.log('Page is valid, opening in-page popup and closing Chrome popup');

      // 发送消息给content script打开注入popup
      chrome.tabs.sendMessage(tab.id, {
        action: 'openInPagePopup'
      }).then(response => {
        console.log('In-page popup response:', response);
        // 无论成功与否都关闭Chrome popup
        window.close();
      }).catch(error => {
        console.error('Failed to open in-page popup:', error);
        // 如果无法打开注入popup，显示错误信息
        updateErrorInfo('popup_creation_failed');
      });
    }
  } catch (error) {
    console.error('Error during popup initialization:', error);
    updateErrorInfo('initialization_error');
  }
})
</script>

<template>
  <div class="popup-container">
    <div class="error-content">
      <h3 class="error-title">{{ errorTitle }}</h3>
      <p class="error-message">{{ errorMessage }}</p>
      <div class="error-actions">
        <button @click="handleRetry" class="retry-btn" v-if="showRetryButton">
          Retry
        </button>
        <button @click="handleClose" class="close-btn">
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.popup-container {
  width: 420px;
  min-height: 160px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
}

.popup-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.error-content {
  padding: 24px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.error-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.error-message {
  margin: 0 0 20px 0;
  font-size: 14px;
  color: #6B7280;
  line-height: 1.5;
  max-width: 360px;
  margin-left: auto;
  margin-right: auto;
}

.error-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.retry-btn, .close-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 70px;
}

.retry-btn {
  background: #4F46E5;
  color: white;
}

.retry-btn:hover {
  background: #4338CA;
  transform: translateY(-1px);
}

.close-btn {
  background: #F3F4F6;
  color: #6B7280;
  border: 1px solid #E5E7EB;
}

.close-btn:hover {
  background: #E5E7EB;
  color: #374151;
  transform: translateY(-1px);
}

.retry-btn:active, .close-btn:active {
  transform: translateY(0);
}
</style>
