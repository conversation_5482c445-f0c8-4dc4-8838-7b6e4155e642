/* 基础样式 */
body {
  width: 380px;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f5f5f5;
}

.container {
  position: relative;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  min-height: 370px;
  border-radius: 0;
}

/* 通用工具类 */
.hidden {
  display: none;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 20px;
  background: #f5f5f5;
}

.header h1 {
  margin: 0;
  color: #2962FF;
  font-size: 22px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 设置按钮样式 */
.settings-button {
  cursor: pointer;
  position: relative;
  padding: 8px;
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-button:hover {
  background: rgba(0, 0, 0, 0.05);
}

.menu-icon {
  width: 18px;
  height: 14px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none;
}

.menu-icon span {
  display: block;
  width: 100%;
  height: 2px;
  background-color: #666;
  border-radius: 2px;
  transition: all 0.2s ease;
  pointer-events: none;
}

/* 登录状态样式 */
.login-status {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
  color: #666;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-dot.logged-in {
  background-color: #4CAF50;
}

.status-dot.logged-out {
  background-color: #f44336;
}

.status-text {
  font-weight: 500;
}

/* 登录提示样式 */
.login-prompt {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.98);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.login-prompt-content {
  text-align: center;
  padding: 30px 15px;
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 320px;
}

.login-logo {
  width: 120px;
  height: 120px;
  margin-bottom: 1rem;
}

.login-prompt-content h2 {
  color: #1D5DF4;
  font-size: 1.5rem;
  margin: 0 0 0.5rem;
  font-weight: 600;
}

.login-prompt-content p {
  color: #666;
  margin: 0 0 1.5rem;
  font-size: 0.9rem;
}

.sign-in-btn {
  background: #1D5DF4;
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.sign-in-btn:hover {
  background: #1850D8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(29, 93, 244, 0.2);
}

.sign-in-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(29, 93, 244, 0.2);
}

/* 用户头像和菜单样式 */
.avatar-menu {
  position: relative;
  cursor: pointer;
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 0;
  display: inline-block;
  vertical-align: middle;
  background-color: #f0f0f0;
  transition: opacity 0.3s ease;
  object-fit: cover;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-img {
  border: 2px solid #4CAF50;
  border-radius: 50%;
  transition: border-color 0.2s ease;
}

.menu-popover {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-10px);
  margin-top: 4px;
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 100px;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transition: all 0.2s ease;
  padding: 2px;
}

/* 添加透明连接区域 */
.menu-popover::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 0;
  right: 0;
  height: 4px;
  background: transparent;
}

/* 三角形指示器 */
.menu-popover::after {
  content: '';
  position: absolute;
  top: -3px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid white;
}

.avatar-menu:hover .menu-popover,
.menu-popover:hover { /* 重要：添加这个选择器 */
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
  pointer-events: auto;
}

.menu-item {
  padding: 6px 12px;
  font-size: 13px;
  color: #333;
  transition: all 0.2s ease;
  cursor: pointer;
  margin: 2px;
  border-radius: 4px;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.sign-out {
  color: #dc2626;
}

/* 表单配置样式 */
#form-config {
  position: relative;
  background: white;
  border-radius: 16px;
  margin: 0 12px 12px 12px;
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 模式选择器样式 */
.mode-buttons {
  position: relative;
  display: flex;
  gap: 4px;
  padding: 4px;
  background: #f5f5f5;
  border-radius: 8px;
}

.mode-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  user-select: none;
}

.mode-btn.active {
  color: #2962FF;
}

/* 添加滑动背景 */
.mode-buttons::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  width: calc((100% - 16px) / 3);
  height: calc(100% - 8px);
  background: white;
  border-radius: 6px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 0;
}

/* 滑动背景位置 */
.mode-buttons:has(#general-mode.active)::after {
  transform: translateX(0);
}

.mode-buttons:has(#email-mode.active)::after {
  transform: translateX(calc(100% + 4px));
}

.mode-buttons:has(#bug-report-mode.active)::after {
  transform: translateX(calc(200% + 8px));
}

/* 按钮点击效果 */
.mode-btn:active {
  transform: scale(0.97);
}

/* 项目选择器样式 */
.project-selector {
  margin: 0;
}

.select-wrapper {
  display: flex;
  gap: 10px;
  align-items: center;
}

select {
  flex: 1;
  padding: 10px 14px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
}

select:focus {
  outline: none;
  border-color: #2962FF;
}

.add-project-btn {
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fff;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 42px;
  transition: all 0.2s ease;
}

.add-project-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

/* 描述输入区域样式 */
.description-input {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.textarea-wrapper {
  width: 100%;
  margin-bottom: 2px;
}

/* 基础文本框样式 */
#form-config .description-input textarea {
  width: 100%;
  padding: 14px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  transition: all 0.3s ease;
  height: 150px;
  box-sizing: border-box;
}

#form-config .description-input textarea:focus {
  outline: none;
  border-color: #2962FF;
}

/* General 模式下的 textarea 高度 */
#form-config .mode-btn.active#general-mode ~ .description-input textarea {
  height: 200px !important;
}

/* Bug Report 模式下的 textarea 高度 */
#form-config .mode-btn.active#bug-report-mode ~ .description-input textarea {
  height: 150px !important;
}

/* 主按钮样式 */
.primary-btn {
  width: 100%;
  padding: 8px;
  border: none;
  border-radius: 8px;
  background: #2962FF;
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.primary-btn:hover:not(.success):not(.loading) {
  background: #1E4EE3;
}

.primary-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.primary-btn:disabled.success {
  opacity: 1;
  background: #2962FF;
}

.primary-btn.loading {
  background: #1E4EE3;
  cursor: not-allowed;
}

.primary-btn.success {
  background: #2962FF;
  cursor: default;
  color: white;
}

/* 按钮内容布局 */
.button-content {
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
  z-index: 1;
}

/* 闪光图标样式 */
.sparkle-icon {
  width: 16px;
  height: 16px;
  color: inherit;
  opacity: 0.9;
  animation: sparkle 2s ease-in-out infinite;
  margin-left: 4px;
  transition: opacity 0.3s ease;
}

.sparkle-icon.hidden {
  opacity: 0;
  pointer-events: none;
}

.primary-btn:hover .sparkle-icon {
  animation: sparkle-hover 1s ease-in-out infinite;
}

/* 加载动画样式 */
.primary-btn .animation {
  display: none;
  position: absolute;
  border-radius: 100%;
  animation: ripple 0.6s linear infinite;
}

.primary-btn.loading .animation {
  display: block;
}

.loading-spinner {
  display: none;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Canvas 样式 */
#confetti-canvas {
  height: 100vh;
  pointer-events: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
}

/* Confetti 样式 */
.confetti-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1000;
}

.confetti {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 2px;
  opacity: 0;
  animation: confetti-burst 0.8s ease-out forwards;
}

.confetti.red { background-color: #FF4136; }
.confetti.blue { background-color: #2962FF; }
.confetti.green { background-color: #2ECC40; }
.confetti.yellow { background-color: #FFDC00; }
.confetti.purple { background-color: #B10DC9; }

/* Toast 样式 */
#status-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 1000;
}

.status-message {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  max-width: 80%;
  text-align: center;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  pointer-events: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.status-message.show {
  opacity: 1;
  transform: translateY(0);
}

.status-message.error {
  background: rgba(244, 67, 54, 0.9);
}

.status-message.success {
  background: rgba(76, 175, 80, 0.9);
}

.status-message.info {
  background: rgba(33, 150, 243, 0.9);
}

/* 动画关键帧 */
@keyframes sparkle {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes sparkle-hover {
  0%, 100% {
    transform: scale(1.1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.2) rotate(10deg);
    opacity: 0.9;
  }
}

@keyframes ripple {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.1),
               0 0 0 40px rgba(255, 255, 255, 0.1),
               0 0 0 80px rgba(255, 255, 255, 0.1),
               0 0 0 120px rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 0 40px rgba(255, 255, 255, 0.1),
               0 0 0 80px rgba(255, 255, 255, 0.1),
               0 0 0 120px rgba(255, 255, 255, 0.1),
               0 0 0 160px rgba(255, 255, 255, 0);
  }
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes confetti-burst {
  0% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translate(var(--tx), var(--ty)) rotate(var(--r));
    opacity: 0;
  }
}

/* Logo 动画效果 */
.login-logo {
  width: 120px;
  height: 120px;
  margin-bottom: 1rem;
}

/* 星星动画 */
.login-logo path[d^="M245.625"] {
  transform-box: fill-box;
  transform-origin: center;
  animation: starPulse 2s ease-in-out infinite;
}

/* 让三个星星错开动画时间 */
.login-logo path[d^="M245.625"]:nth-of-type(1) {
  animation-delay: 0s;
}

.login-logo path[d^="M245.625"]:nth-of-type(2) {
  animation-delay: -0.6s;
}

.login-logo path[d^="M245.625"]:nth-of-type(3) {
  animation-delay: -1.2s;
}

/* 长方形动画 */
.login-logo rect[x="340.211"] {
  transform-box: fill-box;
  transform-origin: left;
  animation: rectStretch 3s ease-in-out infinite;
}

/* 让三个长方形错开动画时间 */
.login-logo rect[x="340.211"]:nth-of-type(1) {
  animation-delay: 0s;
}

.login-logo rect[x="340.211"]:nth-of-type(2) {
  animation-delay: -1s;
}

.login-logo rect[x="340.211"]:nth-of-type(3) {
  animation-delay: -2s;
}

@keyframes starPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);
  }
}

@keyframes rectStretch {
  0%, 100% {
    transform: scaleX(1);
  }
  50% {
    transform: scaleX(1.1);
  }
}

.skip-login-text {
  display: block;
  color: #666;
  font-size: 14px;
  text-decoration: none;
  margin-top: 12px;
  cursor: pointer;
  transition: color 0.2s ease;
}

.skip-login-text:hover {
  color: #1D5DF4;
  text-decoration: underline;
}

/* 语言选择器样式 */
.language-selector {
  margin: 0;
  margin-bottom: -8px;
  display: flex;
  justify-content: flex-end;
}

.language-selector .flex {
  position: relative;
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 0 4px;
  height: 26px;
  border-radius: 4px;
  cursor: pointer;
}

.language-selector select {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.language-selector .selected-language {
  color: #666;
  font-size: 12px;
  margin-right: 2px;
}

/* 调整下拉箭头颜色 */
.language-selector svg {
  color: #666;
  width: 16px;
  height: 16px;
}

/* 调整下拉箭头与文本的间距 */
.language-selector .flex-shrink-0 {
  margin-left: 0;
}

/* 原来的通用样式 */
.hidden {
  display: none;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 保留其他通用样式... */

@keyframes formifyBorderGlow {
  0% {
    outline: 2px solid rgba(33, 150, 243, 0.4);
    box-shadow: 0 0 5px rgba(33, 150, 243, 0.4);
  }
  50% {
    outline: 2px solid rgba(33, 150, 243, 0.8);
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.6);
  }
  100% {
    outline: 2px solid rgba(33, 150, 243, 0.4);
    box-shadow: 0 0 5px rgba(33, 150, 243, 0.4);
  }
}

.formify-loading {
  animation: formifyBorderGlow 1.5s ease-in-out infinite !important;
  z-index: 9999;
}
