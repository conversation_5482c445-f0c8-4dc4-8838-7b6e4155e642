<script lang="ts" setup>
/// <reference types="chrome"/>

import { ref, onMounted } from 'vue'

// 状态管理
const errorTitle = ref('Page Analysis Issue')
const errorMessage = ref('This page cannot be analyzed by Fillify.')
const showRetryButton = ref(false)
const errorReason = ref('')

// 更新错误信息的函数
const updateErrorInfo = (reason: string, pageStatus?: any) => {
  errorReason.value = reason
  
  switch (reason) {
    case 'page_invalid':
      errorTitle.value = 'Page Not Ready'
      errorMessage.value = 'This page cannot be analyzed right now. It might be a blank page, settings page, or needs to be refreshed.'
      showRetryButton.value = true
      break
    case 'popup_creation_failed':
      errorTitle.value = 'Popup Creation Failed'
      errorMessage.value = 'Failed to create the in-page popup. Please try refreshing the page.'
      showRetryButton.value = true
      break
    case 'no_form_fields':
      errorTitle.value = 'No Form Fields Found'
      errorMessage.value = 'No form fields were detected on this page. Please navigate to a page with forms.'
      showRetryButton.value = false
      break
    case 'page_needs_refresh':
      errorTitle.value = 'Page Needs Refresh'
      errorMessage.value = 'Please refresh the page and try again.'
      showRetryButton.value = true
      break
    case 'no_active_tab':
      errorTitle.value = 'No Active Tab'
      errorMessage.value = 'Could not find an active tab to analyze.'
      showRetryButton.value = false
      break
    case 'initialization_error':
      errorTitle.value = 'Initialization Error'
      errorMessage.value = 'Failed to initialize the popup. Please try again.'
      showRetryButton.value = true
      break
    case 'page_still_invalid':
      errorTitle.value = 'Page Still Not Ready'
      errorMessage.value = 'The page is still not ready for analysis. Please refresh the page or navigate to a different page.'
      showRetryButton.value = true
      break
    default:
      errorTitle.value = 'Page Analysis Issue'
      errorMessage.value = 'This page cannot be analyzed by Fillify.'
      showRetryButton.value = false
  }
}

// 事件处理函数
const handleRetry = async () => {
  try {
    // 获取当前标签页
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.id) {
      return
    }

    // 重新检查页面状态
    const pageStatusResponse = await chrome.tabs.sendMessage(tab.id, { type: 'checkPageStatus' })
      .catch(() => {
        return {
          isValid: false,
          needsRefresh: true,
          hasFormFields: false,
          error: 'Communication failed'
        };
      });

    if (pageStatusResponse?.isValid && pageStatusResponse?.hasFormFields) {
      // 页面现在可以分析了，关闭popup
      window.close()
    } else {
      // 页面仍然有问题，更新错误信息
      updateErrorInfo('page_still_invalid', pageStatusResponse)
    }
  } catch (error) {
    console.error('Error during retry:', error)
  }
}

const handleClose = () => {
  window.close()
}

// 初始化函数
onMounted(async () => {
  try {
    // 检查是否是由于注入popup错误触发的
    const errorTrigger = await chrome.storage.local.get('popup_error_trigger');
    if (errorTrigger.popup_error_trigger) {
      const trigger = errorTrigger.popup_error_trigger;
      // 检查时间戳，确保是最近的错误（5秒内）
      if (Date.now() - trigger.timestamp < 5000) {
        console.log('Popup opened due to in-page popup error:', trigger.reason);
        updateErrorInfo(trigger.reason, trigger.pageStatus);
        
        // 清除错误触发信息
        chrome.storage.local.remove('popup_error_trigger');
        return;
      }
    }

    // 如果没有错误触发信息，检查当前页面状态
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tab.id) {
      updateErrorInfo('no_active_tab');
      return;
    }

    // 检查页面状态
    const pageStatusResponse = await chrome.tabs.sendMessage(tab.id, { type: 'checkPageStatus' })
      .catch(() => {
        return {
          isValid: false,
          needsRefresh: true,
          hasFormFields: false,
          error: 'Communication failed'
        };
      });

    if (!pageStatusResponse?.isValid) {
      updateErrorInfo('page_invalid', pageStatusResponse);
    } else if (pageStatusResponse.needsRefresh) {
      updateErrorInfo('page_needs_refresh', pageStatusResponse);
    } else if (!pageStatusResponse.hasFormFields) {
      updateErrorInfo('no_form_fields', pageStatusResponse);
    } else {
      // 页面状态正常，不应该显示Chrome popup，直接关闭
      window.close();
    }
  } catch (error) {
    console.error('Error during popup initialization:', error);
    updateErrorInfo('initialization_error');
  }
})
</script>

<template>
  <div class="popup-container">
    <!-- 错误提示界面 - 这是唯一的界面 -->
    <div class="error-prompt">
      <div class="error-header">
        <div class="logo-container">
          <img src="/icon/icon48.png" alt="Fillify" class="logo">
          <h1>Fillify</h1>
        </div>
      </div>
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h3>{{ errorTitle }}</h3>
        <p>{{ errorMessage }}</p>
        <div class="error-actions">
          <button @click="handleRetry" class="retry-btn" v-if="showRetryButton">
            Retry
          </button>
          <button @click="handleClose" class="close-btn">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.popup-container {
  width: 380px;
  min-height: 300px;
  background: #f5f5f5;
  border-radius: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

.error-prompt {
  padding: 20px;
  text-align: center;
}

.error-header {
  margin-bottom: 20px;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
}

.logo {
  width: 32px;
  height: 32px;
}

.logo-container h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.error-content h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.error-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  text-align: center;
  max-width: 300px;
}

.error-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.retry-btn, .close-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-btn {
  background: #007AFF;
  color: white;
}

.retry-btn:hover {
  background: #0056CC;
}

.close-btn {
  background: #E5E5E7;
  color: #1a1a1a;
}

.close-btn:hover {
  background: #D1D1D6;
}
</style>
